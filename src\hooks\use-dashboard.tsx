/**
 * @file use-dashboard.tsx
 * @description Hook personalizado para gerenciar os dados do dashboard principal,
 * incluindo estatísticas de vendas, estoque, tendências e notificações.
 * Fornece dados em tempo real com atualizações automáticas quando o estoque muda.
 */

import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useEffect, useState } from "react";
import { fetchLowStockProducts } from "@/services/productService";
import { isUserAdmin } from "@/services/userService";

/**
 * Interface que define a estrutura dos dados resumidos do dashboard
 */
export interface DashboardSummary {
  /** Total de itens em estoque */
  stockTotal: number;
  /** Total de vendas realizadas hoje */
  todaySalesTotal: number;
  /** Total de vendas realizadas na última semana */
  weeklySalesTotal: number;
  /** Número de clientes atendidos na última semana */
  customersServed: number;
  /** Tendência do estoque em relação ao período anterior */
  stockTrend: { value: number; isPositive: boolean };
  /** Tendência das vendas de hoje em relação a ontem */
  todayTrend: { value: number; isPositive: boolean };
  /** Tendência das vendas da semana em relação à semana anterior */
  weeklyTrend: { value: number; isPositive: boolean };
  /** Tendência de clientes atendidos em relação ao período anterior */
  customerTrend: { value: number; isPositive: boolean };
  /** Dados de vendas agrupados por dia da semana */
  salesByDay: { name: string; value: number }[];
  /** Dados de estoque agrupados por categoria de produto */
  stockByCategory: { name: string; value: number }[];
  /** Lista de notificações recentes */
  notifications: { id: number; message: string }[];
}

/**
 * Hook personalizado para gerenciar os dados do dashboard
 *
 * @example
 * // Em um componente
 * const { data: dashboardData, isLoading, isError } = useDashboard();
 *
 * // Acessar dados do dashboard
 * if (dashboardData) {
 *   const { stockTotal, todaySalesTotal, weeklySalesTotal } = dashboardData;
 *   // Usar os dados para renderizar o dashboard
 * }
 *
 * @returns {Object} Objeto com dados do dashboard, estado de carregamento e funções de controle
 */
export const useDashboard = () => {
  // Estado local para controlar o total de estoque, permitindo atualizações em tempo real
  const [localStockTotal, setLocalStockTotal] = useState<number | null>(null);

  /**
   * Função para buscar todos os dados necessários para o dashboard
   * Realiza múltiplas consultas ao Supabase para obter informações de estoque,
   * vendas, clientes, categorias e notificações
   *
   * @returns {Promise<DashboardSummary & { lowStockProducts: any[] }>} Dados completos do dashboard
   */
  const fetchDashboardData = async (): Promise<DashboardSummary & { lowStockProducts: any[] }> => {
    // Verificar se o usuário é administrador para personalizar cálculos de tendências
    const isAdmin = await isUserAdmin();
    // Buscar produtos com estoque baixo para alertas
    const lowStockProducts = await fetchLowStockProducts();

    const { data: stockData, error: stockError } = await supabase
      .from('product_variants')
      .select('quantity');

    if (stockError) {
      console.error("Erro ao buscar estoque:", stockError);
      throw stockError;
    }

    const stockTotal = stockData.reduce((sum, item) => sum + item.quantity, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const { data: todaySales, error: todaySalesError } = await supabase
      .from('sales')
      .select('price, sale_date')
      .gte('sale_date', today.toISOString());

    if (todaySalesError) {
      console.error("Erro ao buscar vendas de hoje:", todaySalesError);
      throw todaySalesError;
    }

    const todaySalesTotal = todaySales.reduce((sum, sale) => sum + Number(sale.price), 0);

    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);
    lastWeek.setHours(0, 0, 0, 0);

    const { data: weeklySales, error: weeklySalesError } = await supabase
      .from('sales')
      .select('price, sale_date, customer_name, seller_id')
      .gte('sale_date', lastWeek.toISOString());

    if (weeklySalesError) {
      console.error("Erro ao buscar vendas da semana:", weeklySalesError);
      throw weeklySalesError;
    }

    const weeklySalesTotal = weeklySales.reduce((sum, sale) => sum + Number(sale.price), 0);

    const uniqueCustomers = new Set(weeklySales.map(sale => sale.customer_name));
    const customersServed = uniqueCustomers.size;

    const salesByDay = calculateSalesByDay(weeklySales);

    const { data: stockByCategory, error: stockByCategoryError } = await supabase
      .from('product_variants')
      .select(`
        quantity,
        products!inner (
          name,
          category_id,
          product_categories!inner (
            name
          )
        )
      `);

    if (stockByCategoryError) {
      console.error("Erro ao buscar estoque por categoria:", stockByCategoryError);
      throw stockByCategoryError;
    }

    const stockByCategories = groupStockByCategory(stockByCategory);

    const { data: notifications, error: notificationsError } = await supabase
      .from('notifications')
      .select('id, message')
      .order('created_at', { ascending: false })
      .limit(5);

    if (notificationsError) {
      console.error("Erro ao buscar notificações:", notificationsError);
      throw notificationsError;
    }

    const twoWeeksAgo = new Date(lastWeek);
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 7);

    const { data: previousWeekSales, error: previousWeekError } = await supabase
      .from('sales')
      .select('price, sale_date, customer_name, seller_id')
      .gte('sale_date', twoWeeksAgo.toISOString())
      .lt('sale_date', lastWeek.toISOString());

    if (previousWeekError) {
      console.error("Erro ao buscar vendas da semana anterior:", previousWeekError);
      throw previousWeekError;
    }

    const previousWeekTotal = previousWeekSales.reduce((sum, sale) => sum + Number(sale.price), 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const { data: yesterdaySales, error: yesterdayError } = await supabase
      .from('sales')
      .select('price')
      .gte('sale_date', yesterday.toISOString())
      .lt('sale_date', today.toISOString());

    if (yesterdayError) {
      console.error("Erro ao buscar vendas de ontem:", yesterdayError);
      throw yesterdayError;
    }

    const yesterdayTotal = yesterdaySales.reduce((sum, sale) => sum + Number(sale.price), 0);

    const { data: previousStock, error: previousStockError } = await supabase
      .from('product_variants')
      .select('id, quantity');

    if (previousStockError) {
      console.error("Erro ao buscar dados de estoque anteriores:", previousStockError);
      throw previousStockError;
    }

    const previousUniqueCustomers = new Set(previousWeekSales.map(sale => sale.customer_name));
    const previousCustomersServed = previousUniqueCustomers.size;

    // Se for administrador, calcular médias ponderadas das tendências
    let weeklyTrend, todayTrend, customerTrend;

    if (isAdmin) {
      // Calcular tendências ponderadas para administradores
      weeklyTrend = calculateWeightedTrendForAdmin(weeklySales, previousWeekSales);
      todayTrend = calculateTrend(todaySalesTotal, yesterdayTotal);
      customerTrend = calculateTrend(customersServed, previousCustomersServed);
    } else {
      // Calcular tendências normais para vendedores
      weeklyTrend = calculateTrend(weeklySalesTotal, previousWeekTotal);
      todayTrend = calculateTrend(todaySalesTotal, yesterdayTotal);
      customerTrend = calculateTrend(customersServed, previousCustomersServed);
    }

    const stockTrend = { value: 0, isPositive: true };

    return {
      stockTotal,
      todaySalesTotal,
      weeklySalesTotal,
      customersServed,
      stockTrend,
      todayTrend,
      weeklyTrend,
      customerTrend,
      salesByDay,
      stockByCategory: stockByCategories,
      notifications: notifications.map(n => ({ id: parseInt(n.id.toString().substring(0, 8), 16), message: n.message })),
      lowStockProducts
    };
  };

  /**
   * Calcula a tendência entre dois valores, normalizada para uma escala de 0-100%
   *
   * @param {number} current - Valor atual
   * @param {number} previous - Valor anterior para comparação
   * @returns {Object} Objeto contendo o valor da tendência e se é positiva ou negativa
   */
  const calculateTrend = (current: number, previous: number): { value: number, isPositive: boolean } => {
    // Caso especial: ambos os valores são zero
    if (current === 0 && previous === 0) {
      return { value: 0, isPositive: true };
    }

    // Caso especial: crescimento de zero para algum valor (100% de aumento)
    if (previous === 0 && current > 0) {
      return { value: 100, isPositive: true };
    }

    // Caso especial: queda de algum valor para zero (100% de queda)
    if (current === 0 && previous > 0) {
      return { value: 100, isPositive: false };
    }

    // Cálculo normal da variação percentual
    const percentChange = ((current - previous) / previous) * 100;

    // Normalizar para escala de 0-100%
    const normalizedValue = Math.min(100, Math.abs(Math.round(percentChange)));

    return {
      value: normalizedValue,
      isPositive: percentChange >= 0
    };
  };

  /**
   * Calcula a tendência ponderada das vendas para administradores,
   * considerando o desempenho individual de cada vendedor
   *
   * @param {any[]} currentSales - Vendas do período atual
   * @param {any[]} previousSales - Vendas do período anterior
   * @returns {Object} Tendência ponderada com base no volume de vendas de cada vendedor
   */
  const calculateWeightedTrendForAdmin = (currentSales: any[], previousSales: any[]): { value: number, isPositive: boolean } => {
    // Agrupar vendas por vendedor para análise individual
    const currentBySeller = groupSalesBySeller(currentSales);
    const previousBySeller = groupSalesBySeller(previousSales);

    let totalWeight = 0;
    let weightedSum = 0;
    let isOverallPositive = true;

    // Para cada vendedor, calcular a tendência e aplicar peso baseado no volume de vendas
    for (const [sellerId, currentAmount] of Object.entries(currentBySeller)) {
      const previousAmount = previousBySeller[sellerId] || 0;

      // Calcular a tendência individual
      const trend = calculateTrend(currentAmount, previousAmount);

      // Usar o valor atual como peso (quanto maior o volume de vendas, maior o peso)
      const weight = currentAmount;

      // Acumular a soma ponderada
      weightedSum += trend.value * weight;
      totalWeight += weight;

      // Se qualquer vendedor com peso significativo tiver tendência negativa, considerar negativo
      if (!trend.isPositive && weight > 0) {
        isOverallPositive = false;
      }
    }

    // Se não houver vendas, retornar zero
    if (totalWeight === 0) {
      return { value: 0, isPositive: true };
    }

    // Calcular a média ponderada e normalizar para 0-100%
    const weightedAverage = Math.min(100, Math.round(weightedSum / totalWeight));

    return {
      value: weightedAverage,
      isPositive: isOverallPositive
    };
  };

  /**
   * Agrupa vendas por vendedor e calcula o total de vendas para cada um
   *
   * @param {any[]} sales - Lista de vendas a serem agrupadas
   * @returns {Record<string, number>} Mapa de vendedores com seus totais de vendas
   */
  const groupSalesBySeller = (sales: any[]): Record<string, number> => {
    const sellerMap: Record<string, number> = {};

    sales.forEach(sale => {
      const sellerId = sale.seller_id;
      if (!sellerId) return; // Ignora vendas sem vendedor associado

      if (!sellerMap[sellerId]) {
        sellerMap[sellerId] = 0;
      }

      sellerMap[sellerId] += Number(sale.price) || 0;
    });

    return sellerMap;
  };

  /**
   * Calcula o total de vendas agrupado por dia da semana
   *
   * @param {any[]} sales - Lista de vendas a serem agrupadas
   * @returns {Array<{name: string, value: number}>} Dados formatados para exibição em gráficos
   */
  const calculateSalesByDay = (sales: any[]): { name: string; value: number }[] => {
    const days = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    const dayMap: Record<string, number> = {};

    // Inicializa todos os dias com zero
    days.forEach(day => {
      dayMap[day] = 0;
    });

    // Soma as vendas para cada dia da semana
    sales.forEach(sale => {
      const saleDate = new Date(sale.sale_date);
      const dayName = days[saleDate.getDay()];
      dayMap[dayName] += Number(sale.price);
    });

    // Formata os dados para uso em gráficos
    return days.map(day => ({
      name: day,
      value: dayMap[day]
    }));
  };

  /**
   * Agrupa o estoque por categoria de produto
   *
   * @param {any[]} stockData - Dados de estoque com informações de categoria
   * @returns {Array<{name: string, value: number}>} Dados formatados para exibição em gráficos
   */
  const groupStockByCategory = (stockData: any[]): { name: string; value: number }[] => {
    const categoryMap: Record<string, number> = {};

    // Soma a quantidade de itens para cada categoria
    stockData.forEach(item => {
      const categoryName = item.products.product_categories.name;
      if (!categoryMap[categoryName]) {
        categoryMap[categoryName] = 0;
      }
      categoryMap[categoryName] += item.quantity;
    });

    // Formata os dados para uso em gráficos
    return Object.entries(categoryMap).map(([name, value]) => ({
      name,
      value
    }));
  };

  /**
   * Configuração da consulta React Query para buscar dados do dashboard
   * com atualizações automáticas a cada minuto
   */
  const dashboardQuery = useQuery({
    queryKey: ['dashboard'],
    queryFn: fetchDashboardData,
    refetchInterval: 60000, // Atualiza a cada 1 minuto
    staleTime: 30000 // Considera dados atuais por 30 segundos
  });

  /**
   * Efeito para inicializar o estado local de estoque e configurar
   * listener para mudanças em tempo real na tabela de variantes de produtos
   */
  useEffect(() => {
    // Inicializa o estado local com o valor do dashboard quando disponível
    if (dashboardQuery.data && localStockTotal === null) {
      setLocalStockTotal(dashboardQuery.data.stockTotal);
    }

    // Real-time desabilitado na versão demo
    // Na versão de produção, aqui seria configurado o listener do Supabase Realtime
    // para atualizar automaticamente quando houver mudanças no estoque

    // Limpa o listener quando o componente é desmontado
    return () => {
      // Cleanup não necessário na versão demo
    };
  }, [dashboardQuery.data, localStockTotal, dashboardQuery]);

  // Substitui o valor de estoque total pelos dados em tempo real quando disponível
  if (localStockTotal !== null && dashboardQuery.data) {
    return {
      ...dashboardQuery,
      data: dashboardQuery.data ? {
        ...dashboardQuery.data,
        stockTotal: localStockTotal // Usa o valor local atualizado em tempo real
      } : undefined
    };
  }

  // Retorna os dados originais da consulta quando não há valor local
  return dashboardQuery;
};

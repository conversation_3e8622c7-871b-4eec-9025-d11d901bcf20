Admin:

1.No dashboard: Erro ao carregar dados
Ocorreu um erro ao carregar os dados do dashboard. Por favor, tente novamente mais tarde.

nao aparece nada.

2. No fluxo de caixa:

use-transactions.tsx:52 Erro ao buscar transações: TypeError: supabase.from(...).select(...).order is not a function
    at fetchTransactions (use-transactions.tsx:24:10)
 Error Component Stack
    at CashFlow (CashFlow.tsx:32:7)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)

use-transactions.tsx:72 Erro ao buscar métodos de pagamento: TypeError: supabase.from(...).select(...).order is not a function
    at fetchPaymentMethods (use-transactions.tsx:63:10)
 Error Component Stack
    at CashFlow (CashFlow.tsx:32:7)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
use-transactions.tsx:239 Erro ao buscar usuário: TypeError: supabase.auth.getUser is not a function
    at getUserProfile (use-transactions.tsx:222:54)
    at fetchUserProfile (CashFlow.tsx:136:31)
    at CashFlow.tsx:144:7
 Error Component Stack
    at CashFlow (CashFlow.tsx:32:7)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
use-transactions.tsx:239 Erro ao buscar usuário: TypeError: supabase.auth.getUser is not a function
    at getUserProfile (use-transactions.tsx:222:54)
    at fetchUserProfile (CashFlow.tsx:136:31)
    at CashFlow.tsx:144:7
use-transactions.tsx:52 Erro ao buscar transações: TypeError: supabase.from(...).select(...).order is not a function
    at fetchTransactions (use-transactions.tsx:24:10)
use-transactions.tsx:72 Erro ao buscar métodos de pagamento: TypeError: supabase.from(...).select(...).order is not a function
    at fetchPaymentMethods (use-transactions.tsx:63:10)
use-transactions.tsx:52 Erro ao buscar transações: TypeError: supabase.from(...).select(...).order is not a function
    at fetchTransactions (use-transactions.tsx:24:10)
use-transactions.tsx:72 Erro ao buscar métodos de pagamento: TypeError: supabase.from(...).select(...).order is not a function
    at fetchPaymentMethods (use-transactions.tsx:63:10)
use-transactions.tsx:239 Erro ao buscar usuário: TypeError: supabase.auth.getUser is not a function
    at getUserProfile (use-transactions.tsx:222:54)
    at fetchUserProfile (CashFlow.tsx:136:31)
    at CashFlow.tsx:144:7
 Error Component Stack
    at CashFlow (CashFlow.tsx:32:7)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
use-transactions.tsx:239 Erro ao buscar usuário: TypeError: supabase.auth.getUser is not a function
    at getUserProfile (use-transactions.tsx:222:54)
    at fetchUserProfile (CashFlow.tsx:136:31)
    at CashFlow.tsx:144:7
 Error Component Stack
    at CashFlow (CashFlow.tsx:32:7)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)

3. No estoque:
categoryService.ts:24 Erro ao buscar categorias: TypeError: supabase.from(...).select(...).order is not a function
    at fetchCategories (categoryService.ts:15:8)
 Error Component Stack
    at Inventory (Inventory.tsx:41:59)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)

colorService.ts:26 Erro ao buscar cores: TypeError: supabase.from(...).select(...).order is not a function
    at fetchColors (colorService.ts:16:8)
 Error Component Stack
    at Inventory (Inventory.tsx:41:59)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
sizeService.ts:24 Erro ao buscar tamanhos: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSizes (sizeService.ts:15:8)
 Error Component Stack
    at Inventory (Inventory.tsx:41:59)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)

4. Em vendas nao consigo realizar vendas, as informacoes do modal que abre para realizar a venda nao aparecem. como: produto, status, vendedor. ou seja informacoes que vem das configuracoes


5. em configuracoes: nao aparece nada
colorService.ts:26 Erro ao buscar cores: TypeError: supabase.from(...).select(...).order is not a function
    at fetchColors (colorService.ts:16:8)
 Error Component Stack
    at ColorSettings (ColorSettings.tsx:10:23)
    at div (<anonymous>)
    at _c4 (tabs.tsx:41:6)
    at div (<anonymous>)
    at Settings (Settings.tsx:30:20)
    at AdminRoute (App.tsx:91:23)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)


6. em relatorios:
Erro ao buscar vendas por vendedor: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSalesBySeller (use-reports.tsx:74:10)
 Error Component Stack
    at Reports (Reports.tsx:70:20)
    at AdminRoute (App.tsx:91:23)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)

use-reports.tsx:182 Erro ao buscar vendas por entregador: TypeError: supabase.from(...).select(...).not is not a function
    at fetchSalesByDelivery (use-reports.tsx:137:10)
 Error Component Stack
    at Reports (Reports.tsx:70:20)
    at AdminRoute (App.tsx:91:23)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
use-reports.tsx:242 Erro ao buscar vendas por forma de pagamento: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSalesByPayment (use-reports.tsx:198:10)
 Error Component Stack
    at Reports (Reports.tsx:70:20)
    at AdminRoute (App.tsx:91:23)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
use-reports.tsx:320 Erro ao buscar produtos mais vendidos: TypeError: supabase.from(...).select(...).order is not a function
    at fetchTopProducts (use-reports.tsx:263:10)
 Error Component Stack
    at Reports (Reports.tsx:70:20)
    at AdminRoute (App.tsx:91:23)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
use-reports.tsx:398 Erro ao buscar resumo do período: TypeError: supabase.from(...).select(...).order is not a function
    at fetchPeriodSummary (use-reports.tsx:335:10)
 Error Component Stack
    at Reports (Reports.tsx:70:20)
    at AdminRoute (App.tsx:91:23)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
use-reports.tsx:442 Erro ao buscar opções de filtro: TypeError: supabase.from(...).select(...).in is not a function
    at fetchFilterOptions (use-reports.tsx:408:12)
 Error Component Stack
    at Reports (Reports.tsx:70:20)
    at AdminRoute (App.tsx:91:23)
    at div (<anonymous>)
    at main (<anonymous>)
    at MainContent (App.tsx:256:24)
    at div (<anonymous>)
    at AuthenticatedRoute (App.tsx:57:31)
    at AuthProvider (AuthContext.tsx:144:61)
    at SidebarProvider (SidebarContext.tsx:61:67)
use-reports.tsx:121 Erro ao buscar vendas por vendedor: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSalesBySeller (use-reports.tsx:74:10)
use-reports.tsx:182 Erro ao buscar vendas por entregador: TypeError: supabase.from(...).select(...).not is not a function
    at fetchSalesByDelivery (use-reports.tsx:137:10)
use-reports.tsx:242 Erro ao buscar vendas por forma de pagamento: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSalesByPayment (use-reports.tsx:198:10)
use-reports.tsx:320 Erro ao buscar produtos mais vendidos: TypeError: supabase.from(...).select(...).order is not a function
    at fetchTopProducts (use-reports.tsx:263:10)
use-reports.tsx:398 Erro ao buscar resumo do período: TypeError: supabase.from(...).select(...).order is not a function
    at fetchPeriodSummary (use-reports.tsx:335:10)
use-reports.tsx:442 Erro ao buscar opções de filtro: TypeError: supabase.from(...).select(...).in is not a function
    at fetchFilterOptions (use-reports.tsx:408:12)
use-reports.tsx:121 Erro ao buscar vendas por vendedor: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSalesBySeller (use-reports.tsx:74:10)
use-reports.tsx:182 Erro ao buscar vendas por entregador: TypeError: supabase.from(...).select(...).not is not a function
    at fetchSalesByDelivery (use-reports.tsx:137:10)
use-reports.tsx:242 Erro ao buscar vendas por forma de pagamento: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSalesByPayment (use-reports.tsx:198:10)
use-reports.tsx:320 Erro ao buscar produtos mais vendidos: TypeError: supabase.from(...).select(...).order is not a function
    at fetchTopProducts (use-reports.tsx:263:10)
use-reports.tsx:398 Erro ao buscar resumo do período: TypeError: supabase.from(...).select(...).order is not a function
    at fetchPeriodSummary (use-reports.tsx:335:10)
use-reports.tsx:442 Erro ao buscar opções de filtro: TypeError: supabase.from(...).select(...).in is not a function
    at fetchFilterOptions (use-reports.tsx:408:12)
use-reports.tsx:121 Erro ao buscar vendas por vendedor: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSalesBySeller (use-reports.tsx:74:10)
use-reports.tsx:182 Erro ao buscar vendas por entregador: TypeError: supabase.from(...).select(...).not is not a function
    at fetchSalesByDelivery (use-reports.tsx:137:10)
use-reports.tsx:242 Erro ao buscar vendas por forma de pagamento: TypeError: supabase.from(...).select(...).order is not a function
    at fetchSalesByPayment (use-reports.tsx:198:10)
use-reports.tsx:320 Erro ao buscar produtos mais vendidos: TypeError: supabase.from(...).select(...).order is not a function
    at fetchTopProducts (use-reports.tsx:263:10)
use-reports.tsx:398 Erro ao buscar resumo do período: TypeError: supabase.from(...).select(...).order is not a function
    at fetchPeriodSummary (use-reports.tsx:335:10)
use-reports.tsx:442 Erro ao buscar opções de filtro: TypeError: supabase.from(...).select(...).in is not a function
    at fetchFilterOptions (use-reports.tsx:408:12)

